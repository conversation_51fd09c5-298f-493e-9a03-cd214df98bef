{"name": "status-component", "version": "0.1.0", "private": false, "description": "A framework-agnostic status indicator component with Vue 2 and Vue 3 wrappers.", "main": "dist/status-component.umd.cjs", "module": "dist/status-component.js", "exports": {".": {"import": "./dist/status-component.js", "require": "./dist/status-component.umd.cjs"}, "./vue3": {"import": "./dist/status-component.vue3.js", "require": "./dist/status-component.vue3.umd.cjs"}, "./vue2": {"import": "./dist/status-component.vue2.js", "require": "./dist/status-component.vue2.umd.cjs"}}, "scripts": {"dev:vue3": "vite --config playground/vue3/vite.config.js", "dev:vue2": "vite --config playground/vue2/vite.config.js", "build": "vite build", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "keywords": ["vue", "vue2", "vue3", "status", "indicator", "component"], "author": "Your Name", "license": "MIT", "devDependencies": {"@vitejs/plugin-vue": "^4.0.0", "@vue/babel-plugin-jsx": "^1.0.0", "eslint": "^8.34.0", "eslint-plugin-vue": "^9.9.0", "vite": "^4.1.0", "vue-template-compiler": "^2.7.14", "@vue/composition-api": "^1.7.0", "@vitejs/plugin-vue2": "^2.2.0", "@vitejs/plugin-vue2-jsx": "^2.2.0"}, "peerDependencies": {"vue": "^2.6.14 || ^3.2.0"}, "dependencies": {}}